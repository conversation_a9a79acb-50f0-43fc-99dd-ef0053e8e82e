# api/auth/routes.py
from fastapi import APIRouter, Request, Response, Form, HTTPException
from fastapi.responses import RedirectResponse
from fastapi.templating import Jinja2Templates

from .utils import (
    verify_password, is_user_locked, lock_user,
    increment_failed_attempts, reset_failed_attempts
)
from ..config.settings import settings, user_config, TEMPLATES_DIR

router = APIRouter()
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))


@router.get("/login")
async def login_page(request: Request):
    """登录页面"""
    error = request.query_params.get("error", "")
    return templates.TemplateResponse("login.html", {"request": request, "error": error})


@router.post("/login")
async def login(
    request: Request,
    response: Response,
    username: str = Form(...),
    password: str = Form(...)
):
    """登录处理"""
    try:
        print(f"🔐 Login attempt - Username: '{username}', Password: '{password}'")
        print(f"📋 Available users: {list(user_config.users.keys())}")

        # 检查用户是否存在
        user_data = user_config.get_user(username)
        if not user_data:
            print(f"❌ User '{username}' not found")
            return RedirectResponse(
                url="/login?error=Invalid%20credentials",
                status_code=302
            )

        print(f"✅ User found: {user_data}")

        # 检查用户是否被锁定
        if is_user_locked(username):
            return RedirectResponse(
                url="/login?error=Account%20locked",
                status_code=302
            )

        # 验证密码
        if not verify_password(password, user_data["password"]):
            failed_attempts = increment_failed_attempts(username)

            if failed_attempts >= settings.max_failed_attempts:
                lock_user(username)
                return RedirectResponse(
                    url="/login?error=Account%20locked",
                    status_code=302
                )

            return RedirectResponse(
                url="/login?error=Invalid%20credentials",
                status_code=302
            )

        # 登录成功
        reset_failed_attempts(username)

        response = templates.TemplateResponse("test_form.html", {"request": request})
        response.set_cookie(
            key=settings.auth_cookie_name,
            value=username,
            httponly=True,
            samesite="lax",
            max_age=settings.auth_token_expire_hours * 3600,
            expires=settings.auth_token_expire_hours * 3600
        )
        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login error: {str(e)}")


@router.post("/logout")
async def logout(response: Response):
    """登出"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(settings.auth_cookie_name)
    return response
