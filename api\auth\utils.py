# api/auth/utils.py
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi_login import <PERSON><PERSON><PERSON>ana<PERSON>

from .models import User
from ..config.settings import settings, user_config


def verify_password(plain_password: str, stored_password: str) -> bool:
    """验证密码"""
    return plain_password == stored_password


def load_user(user_id: str) -> Optional[User]:
    """加载用户"""
    user_data = user_config.get_user(user_id)
    if user_data:
        return User(
            username=user_id,
            password=user_data["password"],
            locked_until=user_data.get("locked_until"),
            failed_attempts=user_data.get("failed_attempts", 0)
        )
    return None


def is_user_locked(username: str) -> bool:
    """检查用户是否被锁定"""
    user_data = user_config.get_user(username)
    if not user_data:
        return False

    locked_until = user_data.get("locked_until")
    if locked_until and isinstance(locked_until, datetime):
        return datetime.now() < locked_until
    return False


def lock_user(username: str):
    """锁定用户"""
    lock_until = datetime.now() + timedelta(hours=settings.account_lock_hours)
    user_config.update_user(username, {"locked_until": lock_until})


def increment_failed_attempts(username: str) -> int:
    """增加失败尝试次数"""
    user_data = user_config.get_user(username)
    if user_data:
        failed_attempts = user_data.get("failed_attempts", 0) + 1
        user_config.update_user(username, {"failed_attempts": failed_attempts})
        return failed_attempts
    return 0


def reset_failed_attempts(username: str):
    """重置失败尝试次数"""
    user_config.update_user(username, {
        "failed_attempts": 0,
        "locked_until": None
    })


def is_valid_user(username: str) -> bool:
    """验证用户是否有效且未被锁定"""
    if not username:
        return False

    user_data = user_config.get_user(username)
    if not user_data:
        return False

    # 检查用户是否被锁定
    return not is_user_locked(username)


def create_login_manager() -> LoginManager:
    """创建登录管理器"""
    manager = LoginManager(
        settings.secret_key,
        token_url='/login',
        use_cookie=True
    )
    manager.cookie_name = settings.auth_cookie_name
    manager.user_loader(load_user)
    return manager
